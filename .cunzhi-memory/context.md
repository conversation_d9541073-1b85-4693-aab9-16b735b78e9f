# 项目上下文信息

- Model实体类isShow字段控制模特在前端的展示状态，当设置为0时模特会从所有公开列表中隐藏，但不影响已有订单和收藏关系，主要通过ModelServiceImpl.updateModel方法处理，会记录变更日志到ModelChangeRecord表
- 用户要求重写远程服务中的 getModelsForPreselectCleanup 方法，该方法在 OrderVideoMatchPreselectModelServiceImpl 的 getStatusInvalidPreselectModels 方法中被调用，用于查询状态异常和不展示的预选模特
- 跨数据库查询问题：user_model_blacklist表在biz数据库，order_video_match_preselect_model表在order数据库，不能直接JOIN查询。解决方案是通过Feign服务间调用获取黑名单数据，在应用层进行数据匹配和过滤。
