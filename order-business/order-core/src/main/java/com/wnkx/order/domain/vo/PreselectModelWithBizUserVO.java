package com.wnkx.order.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 预选模特及其对应商家信息VO
 * 用于跨数据库查询的数据传输
 *
 * <AUTHOR>
 * @date 2025/1/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PreselectModelWithBizUserVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("预选模特记录ID")
    private Long id;

    @ApiModelProperty("匹配单ID")
    private Long matchId;

    @ApiModelProperty("模特ID")
    private Long modelId;

    @ApiModelProperty("预选模特状态")
    private Integer status;

    @ApiModelProperty("添加类型")
    private Integer addType;

    @ApiModelProperty("添加时间")
    private Date addTime;

    @ApiModelProperty("添加用户ID")
    private Long addUserId;

    @ApiModelProperty("模特意向")
    private Integer modelIntention;

    @ApiModelProperty("分发结果")
    private Integer distributionResult;

    @ApiModelProperty("订单创建商家用户ID")
    private Long createOrderBizUserId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("淘汰类型")
    private Integer oustType;

    @ApiModelProperty("淘汰时间")
    private Date oustTime;

    @ApiModelProperty("选择状态")
    private Integer selectStatus;

    @ApiModelProperty("选择时间")
    private Date selectTime;

    @ApiModelProperty("分发结果原因")
    private String distributionResultCause;

    @ApiModelProperty("分发结果时间")
    private Date distributionResultTime;

    @ApiModelProperty("模特经纪人ID")
    private Long modelPersonId;

    @ApiModelProperty("模特经纪人姓名")
    private String modelPersonName;

    @ApiModelProperty("是否需要提醒拍摄注意事项")
    private Integer needRemindShootAttention;

    @ApiModelProperty("选择超时")
    private Integer selectTimeout;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;
}
