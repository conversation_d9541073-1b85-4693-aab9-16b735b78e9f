package com.wnkx.order.mapper;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.system.api.domain.dto.order.EnglishStatisticsDTO;
import com.ruoyi.system.api.domain.dto.order.MyPreselectDistributionListDTO;
import com.ruoyi.system.api.domain.dto.order.MyPreselectDockingListDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoMatchPreselectModel;
import com.ruoyi.system.api.domain.vo.order.*;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import com.wnkx.order.domain.vo.PreselectModelWithBizUserVO;

/**
 * 订单_视频_预选模特Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Mapper
public interface OrderVideoMatchPreselectModelMapper extends SuperMapper<OrderVideoMatchPreselectModel> {

    /**
     * 获取
     *
     * @param matchIds
     * @return
     */
    List<OrderVideoMatchPreselectModelVO> selectListVOByMatchIds(@Param("matchIds") List<Long> matchIds);

    /**
     * 查询英文部统计数据
     *
     * @param dto
     * @return
     */
    EnglishStatisticsVO selectEnglishWorkbenchStatistics(@Param("dto") EnglishStatisticsDTO dto);

    /**
     * 查询选定模特列表通过匹配单IDS
     */
    default List<OrderVideoMatchPreselectModel> selectedListByMatchIds(List<Long> matchIds) {
        return this.selectList(new LambdaQueryWrapper<OrderVideoMatchPreselectModel>()
                .in(OrderVideoMatchPreselectModel::getMatchId, matchIds)
                .eq(OrderVideoMatchPreselectModel::getStatus, PreselectStatusEnum.SELECTED.getCode())
                .ne(OrderVideoMatchPreselectModel::getSelectStatus, OrderVideoModelSelectStatusEnum.CANCEL_APPLY.getCode())
        );
    }

    /**
     * 查询选定模特列表通过匹配单ID
     */
    default OrderVideoMatchPreselectModel getSelectedModelByMatchId(Long matchId) {
        return selectOne(new LambdaQueryWrapper<OrderVideoMatchPreselectModel>()
                .eq(OrderVideoMatchPreselectModel::getMatchId, matchId)
                .eq(OrderVideoMatchPreselectModel::getStatus, PreselectStatusEnum.SELECTED.getCode())
                .ne(OrderVideoMatchPreselectModel::getSelectStatus, OrderVideoModelSelectStatusEnum.CANCEL_APPLY.getCode())
        );
    }

    /**
     * 校验是否已存在要添加的模特
     *
     * @param matchId  匹配单ID
     * @param modelIds 模特id
     */
    default List<OrderVideoMatchPreselectModel> checkPreselectModelIsExist(Long matchId, List<Long> modelIds, PreselectModelAddTypeEnum addType) {
        LambdaQueryWrapper<OrderVideoMatchPreselectModel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderVideoMatchPreselectModel::getMatchId, matchId);
        wrapper.in(OrderVideoMatchPreselectModel::getModelId, modelIds);
        if (!PreselectModelAddTypeEnum.MODEL_OPTIONAL.equals(addType)) {
            wrapper.ne(OrderVideoMatchPreselectModel::getStatus, PreselectStatusEnum.OUT.getCode());
        }
        wrapper.ne(OrderVideoMatchPreselectModel::getSelectStatus, OrderVideoModelSelectStatusEnum.CANCEL_APPLY.getCode());
        wrapper.and(wrapper1 ->
                wrapper1
                        .ne(OrderVideoMatchPreselectModel::getAddType, PreselectModelAddTypeEnum.DISTRIBUTION.getCode())
                        .or()
                        .in(OrderVideoMatchPreselectModel::getDistributionResult, DistributionResultEnum.PENDING.getCode(), DistributionResultEnum.WANT.getCode())
        );

        return selectList(wrapper);
    }

    /**
     * 通过视频订单id和模特id查询未对接或已对接的模特
     *
     * @param matchId 匹配单ID
     * @param modelId 模特id
     * @return 未对接的模特
     */
    default OrderVideoMatchPreselectModel getUnJointedOrJointedByVideoIdAndModelId(Long matchId, Long modelId) {
        return this.selectOne(new LambdaQueryWrapper<OrderVideoMatchPreselectModel>()
                .eq(OrderVideoMatchPreselectModel::getMatchId, matchId)
                .eq(OrderVideoMatchPreselectModel::getModelId, modelId)
                .in(OrderVideoMatchPreselectModel::getStatus, PreselectStatusEnum.UN_JOINTED.getCode(), PreselectStatusEnum.JOINTED.getCode())
                .ne(OrderVideoMatchPreselectModel::getAddType, PreselectModelAddTypeEnum.MODEL_OPTIONAL.getCode())
                .ne(OrderVideoMatchPreselectModel::getSelectStatus, OrderVideoModelSelectStatusEnum.CANCEL_APPLY.getCode())
                .last("limit 1")
        );
    }

    /**
     * 获取预选模特的添加人id
     */
    default Set<String> getPreselectUserName(String keyword) {
        List<OrderVideoMatchPreselectModel> matchPreselectModels = selectList(new LambdaQueryWrapper<OrderVideoMatchPreselectModel>()
                .select(OrderVideoMatchPreselectModel::getModelPersonName)
                .like(CharSequenceUtil.isNotBlank(keyword), OrderVideoMatchPreselectModel::getModelPersonName, keyword)
                .isNotNull(OrderVideoMatchPreselectModel::getModelPersonName)
                .ne(OrderVideoMatchPreselectModel::getSelectStatus, OrderVideoModelSelectStatusEnum.CANCEL_APPLY.getCode())
        );
        return matchPreselectModels.stream().map(OrderVideoMatchPreselectModel::getModelPersonName).collect(Collectors.toSet());
    }

    /**
     * 获取预选模特的模特id
     */
    default Set<Long> getPreselectModelId() {
        List<OrderVideoMatchPreselectModel> orderVideos = this.selectList(new LambdaQueryWrapper<OrderVideoMatchPreselectModel>()
                .select(OrderVideoMatchPreselectModel::getModelId)
                .isNotNull(OrderVideoMatchPreselectModel::getModelId)
                .ne(OrderVideoMatchPreselectModel::getSelectStatus, OrderVideoModelSelectStatusEnum.CANCEL_APPLY.getCode())
        );
        return orderVideos.stream().map(OrderVideoMatchPreselectModel::getModelId).collect(Collectors.toSet());
    }

    /**
     * 更新预选模特列表为已淘汰
     */
    default List<OrderVideoMatchPreselectModel> selectListByModelIds(List<Long> modelIds) {
        return selectList(new LambdaQueryWrapper<OrderVideoMatchPreselectModel>()
                .in(OrderVideoMatchPreselectModel::getModelId, modelIds)
                .eq(OrderVideoMatchPreselectModel::getStatus, PreselectStatusEnum.UN_JOINTED.getCode())
                .ne(OrderVideoMatchPreselectModel::getSelectStatus, OrderVideoModelSelectStatusEnum.CANCEL_APPLY.getCode())
        );
    }

    /**
     * 查询匹配单已淘汰模特数量
     */
    default Long countMatchOutModel(Long matchId) {
        return selectCount(new LambdaQueryWrapper<OrderVideoMatchPreselectModel>()
                .eq(OrderVideoMatchPreselectModel::getMatchId, matchId)
                .eq(OrderVideoMatchPreselectModel::getStatus, PreselectStatusEnum.OUT.getCode())
                .ne(OrderVideoMatchPreselectModel::getSelectStatus, OrderVideoModelSelectStatusEnum.CANCEL_APPLY.getCode())
        );
    }

    /**
     * 通过匹配单ID查询预选模特
     */
    default List<OrderVideoMatchPreselectModel> selectListByMatchIds(List<Long> matchIds) {
        return selectList(new LambdaQueryWrapper<OrderVideoMatchPreselectModel>()
                .in(OrderVideoMatchPreselectModel::getMatchId, matchIds)
                .ne(OrderVideoMatchPreselectModel::getSelectStatus, OrderVideoModelSelectStatusEnum.CANCEL_APPLY.getCode())
        );
    }

    /**
     * 通过匹配单ID查询预选模特
     */
    default List<OrderVideoMatchPreselectModel> selectListByMatchId(Long matchId) {
        return selectList(new LambdaQueryWrapper<OrderVideoMatchPreselectModel>()
                .eq(OrderVideoMatchPreselectModel::getMatchId, matchId)
                .ne(OrderVideoMatchPreselectModel::getSelectStatus, OrderVideoModelSelectStatusEnum.CANCEL_APPLY.getCode())
                .orderByDesc(OrderVideoMatchPreselectModel::getAddTime)
        );
    }

    /**
     * 通过匹配单ID查询非淘汰的预选模特
     */
    default List<OrderVideoMatchPreselectModel> selectActivePreselectModelListByMatchIds(List<Long> matchIds) {
        return selectList(new LambdaQueryWrapper<OrderVideoMatchPreselectModel>()
                .in(OrderVideoMatchPreselectModel::getMatchId, matchIds)
                .ne(OrderVideoMatchPreselectModel::getStatus, PreselectStatusEnum.OUT.getCode())
                .ne(OrderVideoMatchPreselectModel::getSelectStatus, OrderVideoModelSelectStatusEnum.CANCEL_APPLY.getCode())
                .and(wrapper ->
                        wrapper.ne(OrderVideoMatchPreselectModel::getAddType, PreselectModelAddTypeEnum.DISTRIBUTION.getCode())
                                .or()
                                .eq(OrderVideoMatchPreselectModel::getModelIntention, ModelIntentionEnum.WANT.getCode())
                )
        );
    }

    /**
     * 查询当前匹配单活跃的预选模特（非淘汰）
     */
    default List<OrderVideoMatchPreselectModel> selectActivePreselectModelListByMatchId(Long matchId) {
        return selectList(new LambdaQueryWrapper<OrderVideoMatchPreselectModel>()
                .eq(OrderVideoMatchPreselectModel::getMatchId, matchId)
                .ne(OrderVideoMatchPreselectModel::getStatus, PreselectStatusEnum.OUT.getCode())
                .ne(OrderVideoMatchPreselectModel::getSelectStatus, OrderVideoModelSelectStatusEnum.CANCEL_APPLY.getCode())
                .orderByDesc(OrderVideoMatchPreselectModel::getAddTime)
        );
    }

    /**
     * 预选管理-模特匹配-我的预选-沟通中
     */
    List<OrderVideoMatchPreselectModel> selectMyPreselectDockingList(@Param("dto") MyPreselectDockingListDTO dto);

    /**
     * 查询非选定非淘汰的预选模特
     */
    default List<OrderVideoMatchPreselectModel> selectUnselectedListByMatchId(Long matchId) {
        return selectList(new LambdaQueryWrapper<OrderVideoMatchPreselectModel>()
                .eq(OrderVideoMatchPreselectModel::getMatchId, matchId)
                .ne(OrderVideoMatchPreselectModel::getStatus, PreselectStatusEnum.SELECTED.getCode())
                .ne(OrderVideoMatchPreselectModel::getStatus, PreselectStatusEnum.OUT.getCode())
                .ne(OrderVideoMatchPreselectModel::getSelectStatus, OrderVideoModelSelectStatusEnum.CANCEL_APPLY.getCode())
        );
    }

    /**
     * 修改订单信息时将预选模特状态从已对接修改为未对接
     */
    default void editOrderVideoPreselectModelFromJointedToUnJointed(Long matchId) {
        update(null, new LambdaUpdateWrapper<OrderVideoMatchPreselectModel>()
                .eq(OrderVideoMatchPreselectModel::getMatchId, matchId)
                .eq(OrderVideoMatchPreselectModel::getStatus, PreselectStatusEnum.JOINTED.getCode())
                .ne(OrderVideoMatchPreselectModel::getSelectStatus, OrderVideoModelSelectStatusEnum.CANCEL_APPLY.getCode())
                .set(OrderVideoMatchPreselectModel::getStatus, PreselectStatusEnum.UN_JOINTED.getCode())
        );
    }

    /**
     * 设置模特选择记录的状态为卖方取消
     */
    default void updateModelSelectStatusToCancelByVideoId(List<Long> machIds) {
        this.update(null, new LambdaUpdateWrapper<OrderVideoMatchPreselectModel>()
                .in(OrderVideoMatchPreselectModel::getMatchId, machIds)
                .ne(OrderVideoMatchPreselectModel::getSelectStatus, OrderVideoModelSelectStatusEnum.CANCEL_APPLY.getCode())
                .in(OrderVideoMatchPreselectModel::getSelectStatus, OrderVideoModelSelectStatusEnum.IN_REVIEW.getCode(), OrderVideoModelSelectStatusEnum.CONFIRM.getCode())
                .set(OrderVideoMatchPreselectModel::getSelectStatus, OrderVideoModelSelectStatusEnum.CANCEL.getCode())
        );
    }

    /**
     * 视频订单回退淘汰预选模特
     */
    default void rollbackOrderOustPreselectModel(Long matchId, String cause) {
        update(null, new LambdaUpdateWrapper<OrderVideoMatchPreselectModel>()
                .eq(OrderVideoMatchPreselectModel::getMatchId, matchId)
                .ne(OrderVideoMatchPreselectModel::getStatus, PreselectStatusEnum.OUT.getCode())
                .ne(OrderVideoMatchPreselectModel::getSelectStatus, OrderVideoModelSelectStatusEnum.CANCEL_APPLY.getCode())
                .set(OrderVideoMatchPreselectModel::getStatus, PreselectStatusEnum.OUT.getCode())
                .set(OrderVideoMatchPreselectModel::getRemark, OrderConstant.ROLLBACK_ORDER + cause)
                .set(OrderVideoMatchPreselectModel::getOustType, PreselectModelOustTypeEnum.ROLLBACK_ORDER.getCode())
                .set(OrderVideoMatchPreselectModel::getOustTime, DateUtil.date())
                .set(OrderVideoMatchPreselectModel::getSelectStatus, OrderVideoModelSelectStatusEnum.CANCEL.getCode())
        );
    }

    /**
     * 通过匹配单ID和模特ID查询未被淘汰的预选模特
     */
    default OrderVideoMatchPreselectModel getActiveByMatchIdAndModelId(Long matchId, Long modelId) {
        return selectOne(new LambdaQueryWrapper<OrderVideoMatchPreselectModel>()
                .eq(OrderVideoMatchPreselectModel::getMatchId, matchId)
                .eq(OrderVideoMatchPreselectModel::getModelId, modelId)
                .ne(OrderVideoMatchPreselectModel::getStatus, PreselectStatusEnum.OUT.getCode())
                .ne(OrderVideoMatchPreselectModel::getSelectStatus, OrderVideoModelSelectStatusEnum.CANCEL_APPLY.getCode())
                .and(wrapper -> wrapper.ne(OrderVideoMatchPreselectModel::getAddType, PreselectModelAddTypeEnum.DISTRIBUTION.getCode())
                        .or()
                        .eq(OrderVideoMatchPreselectModel::getDistributionResult, DistributionResultEnum.WANT.getCode())
                )
        );
    }

    /**
     * 运营修改订单信息时 淘汰原有意向模特
     */
    default List<OrderVideoMatchPreselectModel> selectIntentionModelListByMatchId(Long matchId) {
        return selectList(new LambdaQueryWrapper<OrderVideoMatchPreselectModel>()
                .eq(OrderVideoMatchPreselectModel::getMatchId, matchId)
                .eq(OrderVideoMatchPreselectModel::getAddType, PreselectModelAddTypeEnum.INTENTION_MODEL.getCode())
                .ne(OrderVideoMatchPreselectModel::getStatus, PreselectStatusEnum.OUT.getCode())
                .ne(OrderVideoMatchPreselectModel::getSelectStatus, OrderVideoModelSelectStatusEnum.CANCEL_APPLY.getCode())
        );
    }

    /**
     * 通过视频订单ID和rollbackId检查是否有选定的模特
     */
    boolean checkExistSelectedModelByVideoIdAndRollbackId(@Param("videoId") Long videoId, @Param("rollbackId") Long rollbackId);

    /**
     * 我的预选-沟通中-预选模特下拉框
     */
    Set<Long> myPreselectDockingModelSelect(@Param("currentUserRelevanceModelIds") List<Long> currentUserRelevanceModelIds);

    /**
     * 预选管理-我的预选-分发中-获取待分发模特
     */
    List<MyPreselectDistributionListVO> selectMyPreselectDistributionModelListByCondition(@Param("dto") MyPreselectDistributionListDTO dto);

    /**
     * 预选管理-我的预选-分发中-获取待分发订单
     */
    List<DistributionOrderListVO> selectMyPreselectDistributionOrderListByOvmpmIds(@Param("ovmpmIds") List<Long> ovmpmIds);

    /**
     * 预选管理-我的预选-历史分发记录
     */
    List<DistributionHistoryListVO> selectDistributionHistoryListByModelId(@Param("modelId") Long modelId);

    /**
     * 我的预选-分发中-分发模特下拉框
     */
    List<Long> myPreselectDistributionModelSelect();

    /**
     * 设置已提醒模特拍摄注意事项
     */
    default void setModelShootAttentionRemind(Long preselectModelId) {
        update(null, new LambdaUpdateWrapper<OrderVideoMatchPreselectModel>()
                .eq(OrderVideoMatchPreselectModel::getId, preselectModelId)
                .set(OrderVideoMatchPreselectModel::getNeedRemindShootAttention, StatusTypeEnum.NO.getCode())
        );
    }

    List<UserVO> distributionIssueList();

    /**
     * 查询活跃预选模特及其对应的商家用户信息
     * 用于跨数据库查询黑名单的第一步：获取预选模特和对应的商家ID
     *
     * @return 活跃预选模特及其对应商家信息列表
     */
    List<OrderVideoMatchPreselectModelVO> selectActivePreselectModelsWithBizUser();

    /**
     * 查询被商家拉黑的预选模特记录
     * 通过关联 user_model_blacklist 表查询被对应商家拉黑的预选模特
     * 注意：此方法存在跨数据库查询问题，建议使用 selectActivePreselectModelsWithBizUser + 服务调用的方式
     *
     * @return 被商家拉黑的预选模特列表
     * @deprecated 存在跨数据库查询问题，建议使用新的分步查询方式
     */
    @Deprecated
    List<OrderVideoMatchPreselectModel> selectBlacklistedPreselectModels();

    default void cleanTimeOutTime(Long preselectModelId) {
        update(null, new LambdaUpdateWrapper<OrderVideoMatchPreselectModel>()
                .eq(OrderVideoMatchPreselectModel::getId, preselectModelId)
                .set(OrderVideoMatchPreselectModel::getSelectTimeout, null)
        );
    }
}
