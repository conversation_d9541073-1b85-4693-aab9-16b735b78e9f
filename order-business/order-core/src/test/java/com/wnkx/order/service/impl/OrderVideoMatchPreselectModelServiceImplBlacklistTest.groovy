package com.wnkx.order.service.impl

import com.ruoyi.system.api.domain.entity.order.OrderVideoMatchPreselectModel
import com.ruoyi.system.api.domain.vo.biz.business.user.UserBlackModelVO
import com.ruoyi.system.api.domain.vo.order.MatchBizUserMappingVO
import com.wnkx.order.mapper.OrderVideoMatchMapper
import com.wnkx.order.mapper.OrderVideoMatchPreselectModelMapper
import com.wnkx.order.remote.RemoteService
import spock.lang.Specification
import spock.lang.Subject

import java.lang.reflect.Method

/**
 * OrderVideoMatchPreselectModelServiceImpl.getBlacklistedPreselectModels 方法重构后的单元测试
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
class OrderVideoMatchPreselectModelServiceImplBlacklistTest extends Specification {

    @Subject
    OrderVideoMatchPreselectModelServiceImpl service

    OrderVideoMatchPreselectModelMapper baseMapper = Mock()
    OrderVideoMatchMapper orderVideoMatchMapper = Mock()
    RemoteService remoteService = Mock()

    def setup() {
        service = new OrderVideoMatchPreselectModelServiceImpl(
                remoteService, null, null, null, null, null, null, orderVideoMatchMapper
        )
        service.baseMapper = baseMapper
    }

    def "测试正常的黑名单查询流程"() {
        given: "准备测试数据"
        def allActiveModels = [
                createPreselectModel(1L, 101L, 1001L), // 预选模特1，匹配单101，模特1001
                createPreselectModel(2L, 102L, 1002L), // 预选模特2，匹配单102，模特1002
                createPreselectModel(3L, 103L, 1003L)  // 预选模特3，匹配单103，模特1003
        ]
        
        def cleanupResults = [
                "status_invalid": [createPreselectModel(4L, 104L, 1004L)] // 已处理的数据
        ]

        def matchBizUserMappings = [
                new MatchBizUserMappingVO(matchId: 101L, bizUserId: 2001L),
                new MatchBizUserMappingVO(matchId: 102L, bizUserId: 2002L),
                new MatchBizUserMappingVO(matchId: 103L, bizUserId: 2001L)
        ]

        def blackModelsForUser2001 = [
                new UserBlackModelVO(bizUserId: 2001L, modelId: 1001L), // 商家2001拉黑了模特1001
                new UserBlackModelVO(bizUserId: 2001L, modelId: 1003L)  // 商家2001拉黑了模特1003
        ]

        def allBlackModels = [
                new UserBlackModelVO(bizUserId: 2001L, modelId: 1001L), // 商家2001拉黑了模特1001
                new UserBlackModelVO(bizUserId: 2001L, modelId: 1003L)  // 商家2001拉黑了模特1003
        ]

        when: "调用被测试方法"
        orderVideoMatchMapper.selectBizUserIdsByMatchIds([101L, 102L, 103L] as Set) >> matchBizUserMappings
        remoteService.batchUserBlackModelListByBizUserIds([2001L, 2002L]) >> allBlackModels

        def result = invokePrivateMethod(service, "getBlacklistedPreselectModels", allActiveModels, cleanupResults)

        then: "验证结果"
        result.size() == 2 // 应该返回2个被拉黑的预选模特
        result.find { it.id == 1L } != null // 预选模特1被拉黑
        result.find { it.id == 3L } != null // 预选模特3被拉黑
        result.find { it.id == 2L } == null // 预选模特2未被拉黑
    }

    def "测试排除已处理数据后无剩余数据的情况"() {
        given: "所有数据都已被处理"
        def allActiveModels = [
                createPreselectModel(1L, 101L, 1001L)
        ]
        
        def cleanupResults = [
                "status_invalid": [createPreselectModel(1L, 101L, 1001L)] // 包含所有数据
        ]

        when: "调用被测试方法"
        def result = invokePrivateMethod(service, "getBlacklistedPreselectModels", allActiveModels, cleanupResults)

        then: "应该返回空列表"
        result.isEmpty()
    }

    def "测试Feign服务调用异常的情况"() {
        given: "准备测试数据"
        def allActiveModels = [
                createPreselectModel(1L, 101L, 1001L)
        ]

        def cleanupResults = [:]

        def matchBizUserMappings = [
                new MatchBizUserMappingVO(matchId: 101L, bizUserId: 2001L)
        ]

        when: "调用被测试方法，批量Feign服务抛出异常，降级为单个查询"
        orderVideoMatchMapper.selectBizUserIdsByMatchIds([101L] as Set) >> matchBizUserMappings
        remoteService.batchUserBlackModelListByBizUserIds([2001L]) >> { throw new RuntimeException("批量Feign调用失败") }
        remoteService.userBlackModelListByBizUserId(2001L) >> []

        def result = invokePrivateMethod(service, "getBlacklistedPreselectModels", allActiveModels, cleanupResults)

        then: "应该返回空列表，不抛出异常"
        result.isEmpty()
    }

    def "测试无匹配单映射关系的情况"() {
        given: "准备测试数据"
        def allActiveModels = [
                createPreselectModel(1L, 101L, 1001L)
        ]
        
        def cleanupResults = [:]

        when: "调用被测试方法，无匹配单映射关系"
        orderVideoMatchMapper.selectBizUserIdsByMatchIds([101L] as Set) >> []

        def result = invokePrivateMethod(service, "getBlacklistedPreselectModels", allActiveModels, cleanupResults)

        then: "应该返回空列表"
        result.isEmpty()
    }

    def "测试商家无黑名单的情况"() {
        given: "准备测试数据"
        def allActiveModels = [
                createPreselectModel(1L, 101L, 1001L)
        ]
        
        def cleanupResults = [:]

        def matchBizUserMappings = [
                new MatchBizUserMappingVO(matchId: 101L, bizUserId: 2001L)
        ]

        when: "调用被测试方法，商家无黑名单"
        orderVideoMatchMapper.selectBizUserIdsByMatchIds([101L] as Set) >> matchBizUserMappings
        remoteService.batchUserBlackModelListByBizUserIds([2001L]) >> []

        def result = invokePrivateMethod(service, "getBlacklistedPreselectModels", allActiveModels, cleanupResults)

        then: "应该返回空列表"
        result.isEmpty()
    }

    /**
     * 创建预选模特测试数据
     */
    private OrderVideoMatchPreselectModel createPreselectModel(Long id, Long matchId, Long modelId) {
        def model = new OrderVideoMatchPreselectModel()
        model.id = id
        model.matchId = matchId
        model.modelId = modelId
        return model
    }

    /**
     * 调用私有方法的辅助方法
     */
    private Object invokePrivateMethod(Object target, String methodName, Object... args) {
        Class<?>[] paramTypes = args.collect { it.getClass() } as Class<?>[]
        Method method = target.getClass().getDeclaredMethod(methodName, paramTypes)
        method.setAccessible(true)
        return method.invoke(target, args)
    }
}
