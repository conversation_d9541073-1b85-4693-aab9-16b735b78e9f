package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 匹配单与商家用户映射VO
 * 用于黑名单查询时的数据关联
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@ApiModel(value = "匹配单与商家用户映射VO")
@Data
public class MatchBizUserMappingVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 匹配单ID
     */
    @ApiModelProperty(value = "匹配单ID")
    private Long matchId;

    /**
     * 商家用户ID（创建订单的商家用户ID）
     */
    @ApiModelProperty(value = "商家用户ID")
    private Long bizUserId;
}
