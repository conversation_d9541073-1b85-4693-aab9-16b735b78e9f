<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.business.mapper.UserModelBlacklistMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.model.UserModelBlacklist">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="bizUserId" column="biz_user_id" jdbcType="BIGINT"/>
            <result property="modelId" column="model_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,biz_user_id,model_id,
        create_time
    </sql>

    <select id="userBlackModelList" resultType="com.ruoyi.system.api.domain.vo.biz.business.user.UserBlackModelVO">
        select
            umb.biz_user_id,
            umb.model_id,
            umb.create_time,
            m.model_pic,
            m.name,
            m.nation,
            m.nation_dict,
            m.type,
            m.type_dict
        from user_model_blacklist umb
        left join model m on umb.model_id = m.id
        where umb.biz_user_id = #{bizUserId}
    </select>

    <!-- 批量获取多个用户的黑名单列表 -->
    <select id="batchUserBlackModelList" resultType="com.ruoyi.system.api.domain.vo.biz.business.user.UserBlackModelVO">
        select
            umb.biz_user_id,
            umb.model_id,
            umb.create_time,
            m.model_pic,
            m.name,
            m.nation,
            m.nation_dict,
            m.type,
            m.type_dict
        from user_model_blacklist umb
        left join model m on umb.model_id = m.id
        <where>
            <if test="bizUserIds != null and bizUserIds.size() > 0">
                umb.biz_user_id IN
                <foreach collection="bizUserIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
